# Mantis Clone gRPC Server Dockerfile
FROM node:18-alpine

# Install protobuf compiler
RUN apk add --no-cache protobuf protobuf-dev

# Set working directory
WORKDIR /app

# Copy package files
COPY grpc-package.json package.json
COPY package-lock.json* ./

# Install dependencies
RUN npm install

# Copy proto files
COPY proto/ ./proto/

# Copy source code
COPY src/ ./src/

# Copy database (if exists)
COPY mantis.db* ./

# Create necessary directories
RUN mkdir -p generated logs

# Compile proto files (optional, fallback to runtime compilation)
RUN npm run proto:compile || echo "Proto compilation failed, using runtime compilation"

# Expose gRPC port
EXPOSE 50051

# Set environment variables
ENV GRPC_PORT=50051
ENV NODE_ENV=production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "const grpc = require('@grpc/grpc-js'); const client = new grpc.Client('localhost:50051', grpc.credentials.createInsecure()); client.waitForReady(Date.now() + 1000, (err) => process.exit(err ? 1 : 0));" || exit 1

# Start the server
CMD ["node", "src/grpc-server.js"]
